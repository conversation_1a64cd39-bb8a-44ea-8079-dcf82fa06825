[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=scraping_cli
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --asyncio-mode=auto
markers =
    unit: Unit tests (fast, isolated)
    integration: Integration tests (require external resources)
    slow: Slow running tests
    browserbase: Tests requiring Browserbase API
    async: Async tests 